'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Loader2, Ellipsis, Search, Package } from 'lucide-react';
import { GetPackageBookings } from '@/api/data';
import { StatusBadge } from '@/components/common/status-badge';
import Details from './details';
import { Input } from '@/components/ui/input';
import DateRangeFilter from '@/components/common/date-range-filter';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

const Booking = () => {
  const [open, setOpen] = useState(false);
  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 10 });

  const [detail, setDetail] = useState<any | null>(null);
  const [statusFilter, setStatusFilter] = useState('');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);

  useEffect(() => {
    let params = [];

    if (statusFilter) {
      params.push(statusFilter);
    }

    if (debouncedSearchTerm) {
      params.push(`search=${debouncedSearchTerm}`);
    }

    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      params.push(`startDate=${formattedStartDate}`);
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      params.push(`endDate=${formattedEndDate}`);
    }

    setQueryParam(params.join('&'));
  }, [debouncedSearchTerm, statusFilter, startDate, endDate, setQueryParam]);

  const { bookings, bookingLoading, mutate } = GetPackageBookings(
    `?page=${currentPage}&limit=${pageSize}&${queryParam}`
  );
  const data = bookings?.data?.bookings;
  const totalPages = bookings?.data?.totalPages ?? 0;

  const handleEventFromModal = (booking: any) => {
    setDetail(booking);
    setOpen(true);
  };

  const buttonOptions = [
    { label: 'All', param: '' },
    { label: 'Pending', param: 'status=PENDING' },
    { label: 'Completed', param: 'status=COMPLETED' },
    { label: 'Draft', param: 'status=DRAFT' },
  ];

  const handleSelect = (param: string) => {
    setStatusFilter(param);
  };

  const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    setStartDate(start);
    setEndDate(end);
  };

  return (
    <>
      <div className="flex flex-wrap justify-between items-center mb-4">
        <div className="space-x-2 space-y-3">
          {buttonOptions.map(({ label, param }) => (
            <StatusBadge
              status={label.toLowerCase()}
              className="cursor-pointer"
              key={label}
              onClick={() => handleSelect(param)}
            />
          ))}
        </div>
        <div className="flex flex-wrap gap-3 items-center">
          <DateRangeFilter
            onDateRangeChange={handleDateRangeChange}
            className="w-[250px]"
          />
          <div className="relative w-64">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="w-4 h-4 text-gray-500" />
            </div>
            <Input
              type="text"
              placeholder="Search bookings..."
              className="pl-10 pr-4 py-2 w-full"
              value={searchTerm}
              onChange={handleSearchChange}
            />
          </div>
        </div>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>S/N</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>Reference</TableHead>
              <TableHead>Package</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {bookingLoading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading bookings...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : data && data.length > 0 ? (
              data.map((booking: any, index: number) => (
                <TableRow key={booking.id}>
                  <TableCell>
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </TableCell>
                  <TableCell>
                    {dayjs(booking.createdAt).format('MMMM D, YYYY')}
                  </TableCell>
                  <TableCell>{booking.user.emailAddress}</TableCell>
                  <TableCell>{booking.location}</TableCell>
                  <TableCell>{booking.bookingRef}</TableCell>
                  <TableCell>{booking.packages.name}</TableCell>
                  <TableCell>
                    {StatusBadge({ status: booking.bookingStatus.toLowerCase() })}
                  </TableCell>
                  <TableCell className="text-right">
                    <Ellipsis
                      onClick={() => handleEventFromModal(booking)}
                      className="w-4 h-4 cursor-pointer"
                    />
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <Package className="h-12 w-12 mb-2 opacity-50" />
                    <p className="text-lg font-medium">No bookings found</p>
                    <p className="text-sm">
                      {debouncedSearchTerm || startDate || endDate
                        ? 'Try adjusting your search or date filters'
                        : 'Bookings will appear here once they are created'}
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {totalPages > 1 ? (
        <Pagination
          title="Bookings"
          totalPages={totalPages}
          currentPage={currentPage}
          totalCount={bookings?.data?.totalCount}
          onPageChange={handlePageChange}
        />
      ) : (
        ''
      )}
      {open && (
        <Details open={open} setOpen={setOpen} mutate={mutate} data={detail} />
      )}
    </>
  );
};

export default Booking;
