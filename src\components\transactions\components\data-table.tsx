'use client';

import React, { useState, useEffect } from 'react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';
import dayjs from 'dayjs';
import { Loader2, Ellipsis, Search, CreditCard } from 'lucide-react';
import { GetTransactions, GetStaffTransactions } from '@/api/data';
import { numberFormat } from '@/lib/utils';
import { StatusBadge } from '@/components/common/status-badge';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { hasPermission, PERMISSIONS } from '@/lib/types/permissions';
import Details from './details';
import DateRangeFilter from '@/components/common/date-range-filter';
import MealVoucherModal from './meal-voucher-modal';

const TransactionData = () => {
  const [open, setOpen] = useState(false);
  const [viewType, setViewType] = useState<'my' | 'all'>('my');
  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 10 });

  const [detail, setDetail] = useState<any | null>(null);
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
    const [showMealVoucherModal, setShowMealVoucherModal] = useState(false);


  const canEditTransactions = hasPermission(PERMISSIONS.TRANSACTION_EDIT);
  const canCreateTransactions = hasPermission(PERMISSIONS.TRANSACTION_CREATE);


  // Update query parameters when search term, type filter, status filter, or date range changes
  useEffect(() => {
    let params = [];

    if (typeFilter) {
      params.push(typeFilter);
    }

    if (statusFilter && statusFilter !== 'all') {
      params.push(`status=${statusFilter}`);
    }

    if (debouncedSearchTerm) {
      params.push(`search=${debouncedSearchTerm}`);
    }

    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      params.push(`startDate=${formattedStartDate}`);
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      params.push(`endDate=${formattedEndDate}`);
    }

    setQueryParam(params.join('&'));
  }, [debouncedSearchTerm, typeFilter, statusFilter, startDate, endDate, setQueryParam]);

  // Conditionally use different APIs based on viewType and permissions
  const {
    transactions: myTransactions,
    transactionLoading: myTransactionLoading,
  } = GetStaffTransactions(
    `?page=${currentPage}&limit=${pageSize}&${queryParam}`
  );
  const {
    transactions: allTransactions,
    transactionLoading: allTransactionLoading,
    mutate: allTransactionMutate,
  } = canEditTransactions
    ? GetTransactions(`?page=${currentPage}&limit=${pageSize}&${queryParam}`)
    : { transactions: null, transactionLoading: false };

  // Use appropriate data based on viewType
  const transactions = viewType === 'all' ? allTransactions : myTransactions;
  const transactionLoading =
    viewType === 'all' ? allTransactionLoading : myTransactionLoading;
  const data = transactions?.data?.transactions;
  const totalPages = transactions?.data?.totalPages ?? 0;
  
  // Check for pending transactions in all transactions
  const hasPendingTransactions = canEditTransactions && allTransactions?.data?.transactions?.some(
    (transaction: any) => transaction.status.toLowerCase() === 'pending'
  );

  const handleEventFromModal = (transaction: any) => {
    setDetail(transaction);
    setOpen(true);
  };

    const handleStatusChange = (value: string) => {
    setStatusFilter(value);
  };

    const filterOptions = [
    { value: 'all', label: 'All' },
    { value: 'PENDING', label: 'Pending' },
    { value: 'SUCCESS', label: 'Success' },
    { value: 'FAILED', label: 'Failed' },
    { value: 'PROCESSING', label: 'Processing' },
    { value: 'CONFIRMED', label: 'Confirmed' },
    { value: 'CANCELLED', label: 'Cancelled' },
  ];

  const buttonOptions = [
    { label: 'All', param: '', className: '' },
    {
      label: 'Transfer',
      param: 'type=TRANSFER',
      className:
        'bg-blue-100 text-blue-600 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-400',
    },
    {
      label: 'Withdrawal',
      param: 'type=WITHDRAWAL',
      className:
        'bg-red-100 text-red-600 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-400',
    },
    {
      label: 'Purchase',
      param: 'type=PURCHASE',
      className:
        'bg-orange-100 text-orange-600 hover:bg-orange-200 dark:bg-orange-900/30 dark:text-orange-400',
    },
    {
      label: 'Reward',
      param: 'type=REWARD',
      className:
        'bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/30 dark:text-green-400',
    },
  ];

  const handleSelect = (param: string) => {
    setTypeFilter(param);
  };

  const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    setStartDate(start);
    setEndDate(end);
  };

  return (
    <>
      <div className="w-full overflow-x-auto rounded-t-lg shadow-md">
        <div className="flex flex-wrap justify-between items-center mb-4 p-4">
          <div className="space-x-2 space-y-4">
            {buttonOptions.map(({ label, param, className }) => (
              <StatusBadge
                status={label.toLowerCase()}
                className={`cursor-pointer ${className} ${typeFilter === param ? 'ring-2 ring-offset-1' : ''}`}
                key={label}
                onClick={() => handleSelect(param)}
              />
            ))}
          </div>
          <div className="flex flex-wrap gap-3 items-center">
            <Select value={statusFilter} onValueChange={handleStatusChange}>
                        <SelectTrigger className="w-[220px]">
                          <SelectValue placeholder="Filter by status" />
                        </SelectTrigger>
                        <SelectContent>
                          {filterOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
            <DateRangeFilter
              onDateRangeChange={handleDateRangeChange}
              className="w-[250px]"
            />
            <div className="relative w-64">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Search className="w-4 h-4 text-gray-500" />
              </div>
              <Input
                type="text"
                placeholder="Search transactions..."
                className="pl-10 pr-4 py-2 w-full"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={viewType === 'my' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewType('my')}
              >
                My Transactions
              </Button>
              {canEditTransactions && (
                <Button
                  variant={viewType === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewType('all')}
                  className={`relative ${hasPendingTransactions && viewType !== 'all' ? 'after:absolute after:top-1 after:right-1 after:w-2 after:h-2 after:bg-yellow-500 after:rounded-full' : ''}`}
                >
                  All Transactions
                </Button>
              )}
            {canCreateTransactions && (
                <Button variant="secondary" size="sm" onClick={() => setShowMealVoucherModal(true)}>
                  Meal Voucher
                </Button>
              )}
            </div>
          </div>
        </div>
      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>S/N</TableHead>
              <TableHead>Transaction Date</TableHead>
              <TableHead>Amount Paid</TableHead>
              <TableHead>Reference</TableHead>
              <TableHead>Mode</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transactionLoading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading transactions...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : data && data.length > 0 ? (
              data.map((transaction: any, index: number) => (
                <TableRow key={transaction.id}>
                  <TableCell>
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </TableCell>
                  <TableCell>
                    {dayjs(transaction.createdAt).format('MMMM D, YYYY')}
                  </TableCell>
                  <TableCell>
                    {numberFormat(transaction.amount)}
                  </TableCell>
                  <TableCell>{transaction.reference}</TableCell>
                  <TableCell>{transaction.mode}</TableCell>
                  <TableCell>
                    <span className="px-2 py-1 rounded-full text-[11px] bg-gray-100 text-gray-600">
                      {transaction.type}
                    </span>
                  </TableCell>
                  <TableCell>
                    {StatusBadge({
                      status: transaction.status.toLowerCase(),
                    })}
                  </TableCell>
                  <TableCell className="text-right">
                    <Ellipsis
                      onClick={() => handleEventFromModal(transaction)}
                      className="w-4 h-4 cursor-pointer"
                    />
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <CreditCard className="h-12 w-12 mb-2 opacity-50" />
                    <p className="text-lg font-medium">No transactions found</p>
                    <p className="text-sm">
                      {debouncedSearchTerm || startDate || endDate
                        ? 'Try adjusting your search or date filters'
                        : 'Transactions will appear here once they are created'}
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      </div>
      {totalPages > 1 ? (
        <Pagination
          title="Transactions"
          totalPages={totalPages}
          currentPage={currentPage}
          totalCount={transactions?.data?.totalCount}
          onPageChange={handlePageChange}
        />
      ) : (
        ''
      )}
      {open && <Details open={open} setOpen={setOpen} mutate={allTransactionMutate} data={detail} />}
          {showMealVoucherModal && (
        <MealVoucherModal
          open={showMealVoucherModal}
          setOpen={setShowMealVoucherModal}
          mutate={allTransactionMutate}
        />
      )}
    </>
  );
};

export default TransactionData;
