'use client';

import React, { useState, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Modal } from '@/components/common/modal';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MultiSelect, Option } from '@/components/common/multi-select';
import { CurrencyInputField, InputTextArea } from '@/components/common/form';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { GetLocations } from '@/api/data';
import { GetStaffList } from '@/api/reward/data';
import { Loader2 } from 'lucide-react';

interface MealVoucherModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  mutate?: () => void;
}

// Schemas
const individualSchema = z.object({
  staffIds: z
    .array(z.string())
    .min(1, { message: 'Please select at least one staff member.' }),
  amount: z.string().min(1, { message: 'Amount is required.' }),
  description: z.string().optional(),
});

const locationSchema = z.object({
  locationIds: z
    .array(z.string())
    .min(1, { message: 'Please select at least one location.' }),
  amount: z.string().min(1, { message: 'Amount is required.' }),
  description: z.string().optional(),
});

// Individual Form Component
const IndividualVoucherForm: React.FC<{
  setOpen: (open: boolean) => void;
  mutate?: () => void;
}> = ({ setOpen, mutate }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const { staff: staffsData, staffLoading,  } = GetStaffList(
    `?status=true`
  );

  const form = useForm<z.infer<typeof individualSchema>>({
    resolver: zodResolver(individualSchema),
    defaultValues: { staffIds: [], amount: '', description: '' },
  });

  const staffOptions: Option[] = useMemo(
    () =>
      staffsData?.data?.staffs.map((staff: any) => ({
        value: staff.id,
        label: `${staff.fullName} (${staff.staffCode})`,
      })) || [],
    [staffsData]
  );

  const onSubmit = async (values: z.infer<typeof individualSchema>) => {
    setIsProcessing(true);
    const payload = {
      ...values,
      amount: Number(values.amount.replace(/,/g, '')),
    };
    console.log(payload);
    try {
      const res = await myApi.post(
        '/transaction/send-meal-voucher/individual',
        payload
      );
      if (res.status === 200) {
        toast.success(res.data.data.message);
        mutate?.();
        setOpen(false);
      }
    } catch (error: any) {
      console.log(error);
    } finally {
      setIsProcessing(false);
    }
  };

  if (staffLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="animate-spin" />
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 pt-4">
        <MultiSelect
          options={staffOptions}
          selected={form.watch('staffIds')}
          onChange={(values) =>
            form.setValue('staffIds', values, { shouldValidate: true })
          }
          placeholder="Select staff members..."
          emptyMessage="No staff found."
          className="w-full"
        />
        {form.formState.errors.staffIds && (
          <p className="text-sm font-medium text-destructive">
            {form.formState.errors.staffIds.message}
          </p>
        )}

        <CurrencyInputField
          control={form.control}
          name="amount"
          label="Amount per Staff"
          placeholder="Enter amount"
        />
        <InputTextArea
          control={form.control}
          name="description"
          label="Description (Optional)"
          placeholder="e.g., End of year bonus"
        />

        <div className="flex justify-end">
          <Button type="submit" disabled={isProcessing}>
            {isProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Send Vouchers
          </Button>
        </div>
      </form>
    </Form>
  );
};

// Location Form Component
const LocationVoucherForm: React.FC<{
  setOpen: (open: boolean) => void;
  mutate?: () => void;
}> = ({ setOpen, mutate }) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const { locations: locationsData, isLoading: locationsLoading } =
    GetLocations();

  const form = useForm<z.infer<typeof locationSchema>>({
    resolver: zodResolver(locationSchema),
    defaultValues: { locationIds: [], amount: '', description: '' },
  });

  const locationOptions: Option[] = useMemo(
    () =>
      locationsData?.data?.map((location: any) => ({
        value: location.id,
        label: location.name,
      })) || [],
    [locationsData]
  );

  const onSubmit = async (values: z.infer<typeof locationSchema>) => {
    setIsProcessing(true);
    try {
      const payload = {
        ...values,
        amount: Number(values.amount.replace(/,/g, '')),
      };
      const res = await myApi.post(
        '/transaction/send-meal-voucher/location',
        payload
      );
      if (res.status === 200) {
        toast.success('Meal vouchers sent successfully.');
        mutate?.();
        setOpen(false);
      }
    } catch (error: any) {
      console.log(error);
    } finally {
      setIsProcessing(false);
    }
  };

  if (locationsLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="animate-spin" />
      </div>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 pt-4">
        <MultiSelect
          options={locationOptions}
          selected={form.watch('locationIds')}
          onChange={(values) =>
            form.setValue('locationIds', values, { shouldValidate: true })
          }
          placeholder="Select locations..."
          emptyMessage="No locations found."
          className="w-full"
        />
        {form.formState.errors.locationIds && (
          <p className="text-sm font-medium text-destructive">
            {form.formState.errors.locationIds.message}
          </p>
        )}

        <CurrencyInputField
          control={form.control}
          name="amount"
          label="Amount per Staff in Location"
          placeholder="Enter amount"
        />
        <InputTextArea
          control={form.control}
          name="description"
          label="Description (Optional)"
          placeholder="e.g., End of year bonus"
        />

        <div className="flex justify-end">
          <Button type="submit" disabled={isProcessing}>
            {isProcessing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Send Vouchers
          </Button>
        </div>
      </form>
    </Form>
  );
};

// Main Modal Component
const MealVoucherModal: React.FC<MealVoucherModalProps> = ({
  open,
  setOpen,
  mutate,
}) => {
  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Create Meal Voucher"
      description="Send meal vouchers to individuals or entire locations."
      size="lg"
    >
      <Tabs defaultValue="individual" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="individual">By Individuals</TabsTrigger>
          <TabsTrigger value="location">By Location</TabsTrigger>
        </TabsList>
        <TabsContent value="individual">
          <IndividualVoucherForm setOpen={setOpen} mutate={mutate} />
        </TabsContent>
        <TabsContent value="location">
          <LocationVoucherForm setOpen={setOpen} mutate={mutate} />
        </TabsContent>
      </Tabs>
    </Modal>
  );
};

export default MealVoucherModal;
