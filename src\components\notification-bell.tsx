'use client';

import { useState, useEffect } from 'react';
import { Bell, RefreshCw, X, Trash2 } from 'lucide-react';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { useNotifications } from '@/hooks/useNotifications';
import { Notification } from '@/store/notificationStore';
import Link from 'next/link';

export function NotificationBell() {
  const {
    notifications,
    unreadCount,
    isLoading,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    refresh
  } = useNotifications();
  const [open, setOpen] = useState(false);
  const [pulse, setPulse] = useState(false);

  // Pulse animation when new notifications arrive
  useEffect(() => {
    if (unreadCount > 0) {
      setPulse(true);
      const timer = setTimeout(() => setPulse(false), 2000);
      return () => clearTimeout(timer);
    }
  }, [unreadCount]);

  const handleNotificationClick = async (notification: Notification) => {
    // Only call API if notification is actually unread
    if (!notification.read) {
      await markAsRead(notification.id);
    }
  };

  const handleMarkAllAsRead = async () => {
    await markAllAsRead();
  };

  const handleDeleteNotification = async (e: React.MouseEvent, id: string) => {
    e.stopPropagation(); // Prevent triggering the notification click
    await removeNotification(id);
  };

  const handleClearAll = async () => {
    await clearAll();
  };

  const handleRefresh = () => {
    refresh();
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className={`relative ${pulse ? 'animate-pulse' : ''}`}
        >
          <Bell
            className={`h-5 w-5 ${unreadCount > 0 ? 'text-primary' : ''}`}
          />
          {unreadCount > 0 && (
            <span className="absolute top-0 right-0 h-5 w-5 rounded-full bg-red-500 text-[10px] flex items-center justify-center text-white font-bold shadow-sm">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-72 p-0" align="end">
        <div className="flex items-center justify-between p-3 border-b">
          <h4 className="font-medium">Notifications</h4>
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="sm"
              className="text-xs"
              onClick={handleRefresh}
              title="Refresh notifications"
            >
              <RefreshCw className="h-3 w-3" />
            </Button>
            
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="text-xs"
                onClick={handleMarkAllAsRead}
              >
                Mark all
              </Button>
            )}
            {notifications.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="text-xs text-destructive hover:text-destructive"
                onClick={handleClearAll}
                title="Clear all notifications"
              >
                Clear all
              </Button>
            )}
          </div>
        </div>
        <div className="max-h-80 overflow-auto">
          {notifications.length === 0 ? (
            <div className="p-4 text-center text-muted-foreground">
              No notifications
            </div>
          ) : (
            notifications.map((notification) => (
              <div
                key={notification.id}
                className={`group relative p-4 border-b last:border-0 cursor-pointer hover:bg-muted/50 ${
                  notification.read ? 'bg-background' : 'bg-muted/30'
                }`}
                onClick={() => handleNotificationClick(notification)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    {!notification.read && (
                      <div className="w-2 h-2 rounded-full bg-primary float-right mt-1 ml-2"></div>
                    )}
                    {notification.link ? (
                      <Link href={notification.link} className="block">
                        <h5 className="text-sm font-medium pr-8">{notification.title}</h5>
                        <p className="text-xs text-muted-foreground">
                          {notification.message}
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {format(
                            new Date(notification.timestamp),
                            'MMM d, h:mm a'
                          )}
                        </p>
                      </Link>
                    ) : (
                      <>
                        <h5 className=" text-sm font-medium pr-8">{notification.title}</h5>
                        <p className="text-xs font-semibold text-muted-foreground">
                          {notification.message}
                        </p>
                        <p className="text-xs text-primary mt-1">
                          {format(
                            new Date(notification.timestamp),
                            'MMM d, h:mm a'
                          )}
                        </p>
                      </>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute top-2 right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:bg-destructive/10 hover:text-destructive transition-opacity"
                    onClick={(e) => handleDeleteNotification(e, notification.id)}
                    title="Delete notification"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </PopoverContent>
    </Popover>
  );
}
